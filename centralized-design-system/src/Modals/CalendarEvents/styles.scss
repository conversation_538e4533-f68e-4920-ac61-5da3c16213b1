@import '../../Styles/variables';

.ed-header .ed-dropdown .ed-calendar-events {
  .calendar-grid {
    border: var(--ed-border-size-sm) solid var(--ed-border-color);
    border-radius: var(--ed-border-radius-md);
    width: 100%;
    border-collapse: collapse;

    thead th {
      font-size: var(--ed-font-size-sm);
      font-weight: var(--ed-font-weight-bold);
      text-align: center;
      padding: var(--ed-spacing-xs);
      line-height: calc(var(--ed-line-height-lg) * 1rem);
      border-bottom: var(--ed-border-size-sm) solid var(--ed-border-color);
    }

    tbody tr {
      border-bottom: var(--ed-border-size-sm) solid var(--ed-border-color);

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .cal-day {
    width: calc(100% / 7);
    min-height: rem-calc(137);
    border-right: var(--ed-border-size-sm) solid var(--ed-border-color);
    vertical-align: top;
    padding: var(--ed-spacing-4xs);
    position: relative;
    outline: none;

    &:focus {
      outline: 2px solid var(--ed-primary-base);
      outline-offset: -2px;
      z-index: 1;
    }

    &[aria-selected="true"] {
      background-color: var(--ed-primary-light);
    }

    &.previous {
      .cal-day__date {
        color: var(--ed-state-disabled-color);
      }
    }

    &:nth-child(2n + 1) {
      background-color: var(--ed-gray-1);
    }

    // Make items in last row float on top
    tr:nth-last-child(-n+2) & {
      .cal-event__modal {
        bottom: rem-calc(20);
      }
    }

    &:nth-child(5),
    &:nth-child(6),
    &:nth-child(7) {
      .cal-event__modal {
        right: rem-calc(20);
      }
    }

    &:nth-child(7) {
      border-right: none;
    }
  }

  .cal-day__date {
    font-size: var(--ed-font-size-bade);
    color: var(--ed-text-color-primary);
    margin: var(--ed-spacing-2xs);
  }

  .cal-event {
    font-size: rem-calc(8);
    line-height: rem-calc(11);
    padding: var(--ed-spacing-4xs);
    background: var(--ed-white);
    margin-bottom: var(--ed-spacing-4xs);
    cursor: pointer;

    label {
      position: relative;
      cursor: pointer;
      color: var(--ed-body-color);
      margin-top: 0;
      color: var(--ed-text-color-primary);

      span {
        text-overflow: ellipsis;
        overflow: hidden;
        width: 100%;
        display: inline-block;
        line-height: 1.2;
        margin-bottom: calc(-1 * var(--ed-spacing-5xs));
        font-weight: normal;
      }
    }

    &.registered {
      background: var(--ed-primary-base);

      label {
        color: var(--ed-text-color-primary-light);
      }
    }

    &.event-view-more {
      background: none;
      border: none;
      font-weight: bold;
      margin-top: 0.1rem;

      > label > span {
        text-align: center;
        font-weight: var(--ed-font-weight-bold);
        font-size: var(--ed-font-size-xs);
        color: var(--ed-primary-base);
      }
    }

    .cal-event__modal {
      position: absolute;
      background: var(--ed-white);
      border: var(--ed-border-size-sm) solid var(--ed-border-color);
      border-radius: var(--ed-border-radius-lg);
      padding: var(--ed-font-size-lg);
      display: none;
      z-index: 1;
      width: calc(8.125rem * 3.5);
      color: var(--ed-body-color);
      cursor: default;
      h5 {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      &.modal-view-more {
        width: rem-calc(130);
        padding: var(--ed-spacing-3xs);
        transform: translateY(-50%);

        .modal-view-more__header {
          text-align: center;
          font-size: var(--ed-font-size-base);
          line-height: calc(var(--ed-line-height-base) * 1rem);
          margin-top: var(--ed-spacing-xl);
          margin-bottom: var(--ed-spacing-2xs);
          font-weight: var(--ed-font-weight-normal);
        }
      }

      a {
        font-size: var(--ed-font-size-base);
        color: var(--ed-primary-base);
      }

      a:hover,
      a:focus {
        text-decoration: underline;
      }

      &:after {
        content: '×';
        position: absolute;
        top: var(--ed-spacing-base);
        right: var(--ed-spacing-base);
        cursor: pointer;
        font-size: 1.8rem;
        color: var(--ed-text-color-supporting);
      }
    }

    input {
      width: 0;
    }

    input:checked + .cal-event__modal {
      display: flex;
      flex-direction: column;
    }

    &.approved,
    &.pending,
    &.active {
      background-color: var(--ed-primary-base);
      border-color: var(--ed-primary-base);

      label {
        color: var(--ed-text-color-primary-light);
      }
    }
  }

  .calendar-nav {
    margin: var(--ed-spacing-xl) var(--ed-spacing-xl) var(--ed-spacing-xl) 0;

    .calendar-nav__month {
      font-weight: bold;
      display: flex;
      align-items: center;
      margin-bottom: var(--ed-spacing-xl);

      [role='button'] {
        margin: 0 var(--ed-spacing-xs);
        font-size: var(--ed-font-size-2xl);
        cursor: pointer;
      }
    }
  }

  .spinner-container {
    background: rgba(255, 255, 255, 0.8);
    margin-left: calc(-1 * var(--ed-spacing-lg));
    margin-top: rem-calc(70);
    height: calc(100% - 4.375rem);
    justify-content: flex-start;
    position: absolute;
    top: 0;
    width: 100%;

    img {
      margin-top: rem-calc(200);
    }
  }
}
